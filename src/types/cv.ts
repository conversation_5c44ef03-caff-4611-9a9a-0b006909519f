export interface PersonalInfo {
  name: string;
  title: string;
  phone: string;
  city: string;
  degree: string;
  profileImage: string;
  logo: string;
  socialLinks: SocialLink[];
}

export interface SocialLink {
  platform: string;
  url: string;
  icon: string;
}

export interface Skill {
  name: string;
  percentage: number;
  category: SkillCategory;
}

export type SkillCategory =
  | 'webs'
  | 'frameworks'
  | 'prog'
  | 'db'
  | 'apis'
  | 'sistem'
  | 'ofimatica'
  | 'otro';

export interface Education {
  title: string;
  period: string;
  institution: string;
  location: string;
}

export interface ComplementaryEducation extends Education {}

export interface Experience {
  title: string;
  period: string;
  company: string;
  location: string;
  responsibilities: string[];
}

export interface PortfolioItem {
  id: string;
  title: string;
  category: string;
  image: string;
  description?: string;
  link?: string;
}

export interface Service {
  icon: string;
  title: string;
  description: string;
}

export interface CVData {
  personalInfo: PersonalInfo;
  about: string;
  skills: Skill[];
  education: Education[];
  complementaryEducation: ComplementaryEducation[];
  experience: Experience[];
  portfolio: PortfolioItem[];
  services: Service[];
}
